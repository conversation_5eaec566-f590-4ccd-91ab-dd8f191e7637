import { FileCode } from 'lucide-react';
import { CellContext, createColumnHelper, Row } from '@tanstack/react-table';
import _ from 'lodash';
import clsx from 'clsx';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { RoleTypes } from '@/portainer/rbac/models/role';
import Podman from '@/assets/ico/vendor/podman.svg?c';
import Docker from '@/assets/ico/vendor/docker.svg?c';
import Kubernetes from '@/assets/ico/vendor/kubernetes.svg?c';

import { Datatable, Table } from '@@/datatables';
import { createPersistedStore } from '@@/datatables/types';
import { useTableState } from '@@/datatables/useTableState';
import { BEFeatureIndicator } from '@@/BEFeatureIndicator';
import { Badge } from '@@/Badge';
import { Tooltip } from '@@/Tip/Tooltip';

import { isBE } from '../../feature-flags/feature-flags.service';
import { FeatureId } from '../../feature-flags/enums';

import { Platform, RbacRole } from './types';

const tableKey = 'rbac-roles-table';

const store = createPersistedStore(tableKey);

export function RbacRolesDatatable({
  dataset,
}: {
  dataset: Array<RbacRole> | undefined;
}) {
  const { t } = useTranslation();
  const tableState = useTableState(store, tableKey);
  const columns = getColumns(t);

  return (
    <Datatable
      title={t('rbac.roles.title', 'Roles')}
      titleIcon={FileCode}
      dataset={dataset || []}
      columns={columns}
      isLoading={!dataset}
      settingsManager={tableState}
      disableSelect
      data-cy="rbac-roles-datatable"
      renderRow={renderRow}
    />
  );
}

function renderRow(row: Row<RbacRole>, highlightedItemId?: string) {
  return (
    <Table.Row<RbacRole>
      cells={row.getVisibleCells()}
      className={clsx('[&>td]:!py-4 [&>td]:!align-top', {
        active: highlightedItemId === row.id,
      })}
    />
  );
}

function getColumns(t: (key: string, defaultValue?: string) => string) {
  const columnHelper = createColumnHelper<RbacRole>();

  return _.compact([
    columnHelper.accessor('Name', {
      header: t('rbac.name', 'Name'),
    }),
    columnHelper.accessor('Description', {
      header: t('rbac.description', 'Description'),
    }),
    columnHelper.accessor('Scope', {
      header: t('rbac.scope', 'Scope'),
      cell: ScopeCell,
    }),
    !isBE &&
      columnHelper.display({
        id: 'be-indicator',
        cell: ({ row: { original: item } }) =>
          item.Id === RoleTypes.STANDARD ? (
            <b>{t('rbac.default', 'Default')}</b>
          ) : (
            <BEFeatureIndicator featureId={FeatureId.RBAC_ROLES} />
          ),
        meta: {
          className: 'text-center',
        },
      }),
  ]);
}

function ScopeCell({
  row: { original: item },
}: CellContext<RbacRole, RbacRole['Scope'] | undefined>) {
  const orderedScopes = useMemo(() => {
    if (!item.Scope) {
      return [];
    }
    return Object.keys(item.Scope).sort((a, b) =>
      a.localeCompare(b)
    ) as Platform[];
  }, [item.Scope]);

  if (orderedScopes.length === 0) {
    return null;
  }

  return (
    <div className="flex flex-wrap gap-1">
      {orderedScopes.map((scope) => (
        <ScopeItem
          key={scope}
          scopeName={scope}
          scopeValues={item.Scope[scope]}
        />
      ))}
    </div>
  );
}

function ScopeItem({
  scopeName,
  scopeValues,
}: {
  scopeName: string;
  scopeValues: string[];
}) {
  const assignedResources = scopeValues.filter((value) => value !== '*');

  let Icon;

  switch (scopeName) {
    case 'kubernetes':
      Icon = Kubernetes;
      break;
    case 'podman':
      Icon = Podman;
      break;
    default:
      Icon = Docker;
  }

  return (
    <Badge type="muted">
      <Icon className="mr-1 h-4 w-4" />
      {_.capitalize(scopeName)}
      {assignedResources.length > 0 && (
        <>
          : assigned resources{' '}
          <span className="text-gray-6 flex items-center">
            <Tooltip message={assignedResources.join(', ')} />
          </span>
        </>
      )}
    </Badge>
  );
}
